import {last, uniq} from 'lodash';
import {Chat, ChatMessage, MessageTask} from '@/types/staff/chat';
import {ChatStage} from '@/types/staff/stage';
import {DEFAULT_TASK_ID} from '@/constants/staff';
import {getCurrentChatId, getSdkMessageListener} from './chatSdk';
import {getCurrentChat, getTask, setChat, setMessage, setStage, setTask} from './chat';

// eslint-disable-next-line complexity
const messageReducer = (chat: Chat, message: ChatMessage) => {
    const {messageIds = [], stageIds = {}, taskIds = []} = chat;
    const messageTaskIds = (message.tasks ?? []).map(task => task.id);

    // 这里判断逻辑根本不知道为什么
    const hasNewMeta = message?.meta?.title
        // 下边两个场景是为了应对meta变更的影响，主要是因为不知道为什么上边会是那个逻辑
        || message?.meta?.disabled !== chat?.meta?.disabled
        || message?.meta?.cancelDisabled !== chat?.meta?.cancelDisabled;

    const hasNewMessage = !messageIds.includes(message.messageId);

    let hasNewStage = false;
    const allStagesWithTaskId = (message.tasks ?? []).reduce((acc, task) => {
        if (task.result) {
            acc.push({
                stage: task.result,
                taskId: task.id,
            });
        }
        return acc;
    }, [] as Array<{stage: ChatStage, taskId: string}>);

    const nextStageIds = allStagesWithTaskId.reduce(
        (acc, {stage, taskId}) => {
            if (!acc[taskId]) {
                acc[taskId] = [];
            }
            if (!acc[taskId].includes(stage.id)) {
                hasNewStage = true;
                acc[taskId].push(stage.id);
            }
            return acc;
        },
        stageIds
    );

    if (!hasNewMeta && !hasNewMessage && !hasNewStage && (!chat.running && message.finish)) {
        return chat;
    }

    const mergedTaskIds = uniq([...taskIds, ...messageTaskIds]);
    const hasNewTask = messageTaskIds.some(taskId => !taskIds.includes(taskId));

    // 对于旧的数据，如果任务id数组是空的，但是有stageId，那么赋予其默认的任务id
    const hasStages = allStagesWithTaskId.length > 0;
    const nextTaskIds = (!mergedTaskIds.length && hasStages)
        ? [DEFAULT_TASK_ID]
        : mergedTaskIds;
    const nextCurrentTaskId = (!mergedTaskIds.length && hasStages)
        ? DEFAULT_TASK_ID
        : hasNewTask ? last(nextTaskIds) : chat.currentTaskId;
    return {
        ...chat,
        running: (message.role === 'USER' && !['callback', 'agent'].includes(message.source))
            || (message.role === 'AGENT' && !message.finish),
        meta: hasNewMeta ? message.meta : chat.meta,
        messageIds: hasNewMessage ? [...messageIds, message.messageId] : messageIds,
        taskIds: nextTaskIds,
        currentTaskId: nextCurrentTaskId,
        stageIds: nextStageIds,
        currentStageId: hasNewStage ? last(nextStageIds?.[nextCurrentTaskId]) : chat.currentStageId,
    };

};

function normalizeMessage(message: ChatMessage): ChatMessage {
    if (message.version === 'v2') {
        return message;
    }

    return {
        ...message,
        version: 'v2',
        tasks: message.tasks ? message.tasks.map((task: MessageTask) => {
            if (task.result) {
                return task;
            }

            const resultStage = message.stages ? message.stages.find(stage => stage.taskId === task.id
                && (stage.id === '结果预览' || stage.name === '结果预览'))
                ?? message.stages.find(stage => stage.id === '结果预览' || stage.name === '结果预览') : undefined;

            return {
                ...task,
                result: resultStage,
            };
        }) : message.tasks,
    };
}

// 每次 sse 到达的时候触发这个函数
export const messageArrived = async (message: ChatMessage) => {
    const normalizedMessage = normalizeMessage(message);
    setMessage(normalizedMessage.messageId, normalizedMessage);

    const allStagesWithTaskId = (normalizedMessage.tasks ?? []).reduce((acc, task) => {
        if (task.result) {
            acc.push({
                stage: task.result,
                taskId: task.id,
            });
        }
        return acc;
    }, [] as Array<{stage: ChatStage, taskId: string}>);

    allStagesWithTaskId.forEach(({stage, taskId}) => {
        // 先更新 stage
        setStage(taskId, stage.id, {...stage, messageId: normalizedMessage.messageId, taskId});
    });
    // 多任务更新tasks
    normalizedMessage?.tasks?.forEach(task => {
        setTask(task.id, {...task, messageId: normalizedMessage.messageId});
    });
    // 单任务更新tasks
    if (!normalizedMessage?.tasks?.length && allStagesWithTaskId.length) {
        const defaultTask = getTask(DEFAULT_TASK_ID);
        setTask(DEFAULT_TASK_ID, {
            id: DEFAULT_TASK_ID,
            name: normalizedMessage?.meta?.title || defaultTask?.name || '默认任务',
            status: normalizedMessage?.meta?.status || defaultTask?.status,
            statusText: normalizedMessage?.meta?.statusText || defaultTask?.statusText,
            messageId: normalizedMessage.messageId,
        });
    }

    const conversationId = getCurrentChatId();
    await new Promise(resolve => setTimeout(resolve, 200));
    setChat(conversationId, chat => messageReducer(chat, normalizedMessage));
    const listeners = getSdkMessageListener(conversationId);
    if (listeners?.length) {
        listeners.forEach(listener => listener(normalizedMessage));
    }
};

export const handleRemoveMessage = (messageId: string) => {
    const currentChat = getCurrentChat();
    const {conversationId} = currentChat;

    setChat(conversationId, item => {
        const {messageIds = []} = currentChat;
        const newMessageIds = messageIds.filter(id => id !== messageId);
        return ({
            ...item,
            messageIds: newMessageIds,
        });
    });
};
