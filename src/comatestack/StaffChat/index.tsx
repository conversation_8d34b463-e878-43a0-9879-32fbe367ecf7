import styled from '@emotion/styled';
import {useEffect, useState} from 'react';
import {ResizeLayout} from '@/design/ResizeLayout';
import {useShowHistory} from '@/regions/staff/showHistory';
import {colors} from '@/constants/colors';
import {staffColors} from '@/constants/colors/staff';
import {
    useSupportAreaCollapse,
    useSupportAreaFull,
} from '@/regions/staff/supportArea';
import {
    useAnchorAssociation,
    useChatId,
    useChatWebsocketEffect,
} from '@/components/Chat/hooks';
import {useCurrentChat, getTask} from '@/regions/staff/chat';
import {setCurrentChatId} from '@/regions/staff/chatSdk';
import {changeConversationId} from '@/regions/staff/chatHandlers';
import {SupportArea} from './SupportArea';
import ChatArea from './ChatArea';

const Container = styled.div`
    display: flex;
    height: 100vh;
    width: 100%;
    flex-direction: column;
    overflow: hidden;
    border-top-left-radius: 16px;
    border-bottom-left-radius: 16px;
    background: ${colors.white};

    [data-resize-handle-state="hover"],
    [data-resize-handle-state="drag"] {
        .devops-resize-handle-line {
            background-color: ${staffColors.primary};
            opacity: 0.3;
        }
    }
`;

const BgDiv = styled.div`
    border-radius: 1141px;
    background: radial-gradient(
        50% 50% at 50% 50%,
        rgba(72, 84, 253, 0.3) 0%,
        rgba(138, 138, 138, 0.3) 100%
    );
    filter: blur(45px);
    height: 30px;
    flex-shrink: 0;
    position: fixed;
    width: 75%;
    z-index: 1;
    top: 37px;
    align-self: center;
`;

const useAllTasksCompleted = (taskIds: string[] = []) => {
    const [allCompleted, setAllCompleted] = useState(false);

    useEffect(
        () => {
            if (!taskIds || taskIds.length === 0) {
                setAllCompleted(false);
                return;
            }
            const tasks = taskIds.map(taskId => getTask(taskId)).filter(Boolean);
            const completed = tasks.every(task => {
                return task && task.result && task.status !== 'failed';
            });
            setAllCompleted(completed);
        },
        [taskIds]
    );

    return allCompleted;
};

const StaffChat = () => {
    const chatId = useChatId();
    const showHistory = useShowHistory();
    const supportAreaFull = useSupportAreaFull();
    const supportAreaCollapse = useSupportAreaCollapse();
    const {taskIds} = useCurrentChat();
    const allTasksCompleted = useAllTasksCompleted(taskIds ?? []);

    useEffect(
        () => {
            if (chatId) {
                setCurrentChatId(chatId);
                changeConversationId(chatId);
            }
        },
        [chatId]
    );

    useChatWebsocketEffect();
    useAnchorAssociation();

    const hasValidTasks = (taskIds ?? []).length > 0;
    const hideRight = !hasValidTasks || !allTasksCompleted || showHistory || supportAreaCollapse;

    return (
        <Container>
            {!showHistory && <BgDiv />}
            <ResizeLayout
                left={supportAreaFull ? null : <ChatArea />}
                right={!hideRight && <SupportArea />}
                leftProps={{defaultSize: 37, minSize: 30, maxSize: 45}}
            />
        </Container>
    );
};

export default StaffChat;
